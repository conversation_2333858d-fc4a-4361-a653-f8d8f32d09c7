import 'package:flutter_test/flutter_test.dart';
import 'package:tubewell_water_billing/models/bill.dart';
import 'package:tubewell_water_billing/models/payment.dart';

void main() {
  group('Bills Summary Calculation Tests', () {
    test('should correctly calculate paid and unpaid amounts for fully paid bills', () {
      // Create test bills
      final bills = [
        Bill(
          id: 1,
          customerId: 1,
          billDate: DateTime.now(),
          startTime: DateTime.now(),
          endTime: DateTime.now().add(const Duration(hours: 2)),
          durationHours: 2.0,
          durationHoursWhole: 2,
          durationMinutes: 0,
          hourlyRate: 100.0,
          amount: 200.0,
          isPaid: true,
        ),
        Bill(
          id: 2,
          customerId: 1,
          billDate: DateTime.now(),
          startTime: DateTime.now(),
          endTime: DateTime.now().add(const Duration(hours: 3)),
          durationHours: 3.0,
          durationHoursWhole: 3,
          durationMinutes: 0,
          hourlyRate: 100.0,
          amount: 300.0,
          isPaid: false,
        ),
      ];

      // Manual calculation logic (same as in database_service.dart)
      double manualPaidAmount = 0.0;
      double manualUnpaidAmount = 0.0;
      int manualPaidCount = 0;
      int manualUnpaidCount = 0;

      for (var bill in bills) {
        if (bill.isPaid) {
          manualPaidAmount += bill.amount;
          manualPaidCount++;
        } else if (bill.isPartiallyPaid && bill.partialAmount != null) {
          manualPaidAmount += bill.partialAmount!;
          manualUnpaidAmount += (bill.amount - bill.partialAmount!);
          manualUnpaidCount++;
        } else {
          manualUnpaidAmount += bill.amount;
          manualUnpaidCount++;
        }
      }

      // Verify calculations
      expect(manualPaidAmount, equals(200.0));
      expect(manualUnpaidAmount, equals(300.0));
      expect(manualPaidCount, equals(1));
      expect(manualUnpaidCount, equals(1));
    });

    test('should correctly calculate paid and unpaid amounts for partially paid bills', () {
      // Create test bills with partial payments
      final bills = [
        Bill(
          id: 1,
          customerId: 1,
          billDate: DateTime.now(),
          startTime: DateTime.now(),
          endTime: DateTime.now().add(const Duration(hours: 2)),
          durationHours: 2.0,
          durationHoursWhole: 2,
          durationMinutes: 0,
          hourlyRate: 100.0,
          amount: 200.0,
          isPaid: false,
          isPartiallyPaid: true,
          partialAmount: 150.0,
        ),
        Bill(
          id: 2,
          customerId: 1,
          billDate: DateTime.now(),
          startTime: DateTime.now(),
          endTime: DateTime.now().add(const Duration(hours: 3)),
          durationHours: 3.0,
          durationHoursWhole: 3,
          durationMinutes: 0,
          hourlyRate: 100.0,
          amount: 300.0,
          isPaid: true,
        ),
      ];

      // Manual calculation logic
      double manualPaidAmount = 0.0;
      double manualUnpaidAmount = 0.0;
      int manualPaidCount = 0;
      int manualUnpaidCount = 0;

      for (var bill in bills) {
        if (bill.isPaid) {
          manualPaidAmount += bill.amount;
          manualPaidCount++;
        } else if (bill.isPartiallyPaid && bill.partialAmount != null) {
          manualPaidAmount += bill.partialAmount!;
          manualUnpaidAmount += (bill.amount - bill.partialAmount!);
          manualUnpaidCount++;
        } else {
          manualUnpaidAmount += bill.amount;
          manualUnpaidCount++;
        }
      }

      // Verify calculations
      expect(manualPaidAmount, equals(450.0)); // 150 + 300
      expect(manualUnpaidAmount, equals(50.0)); // 200 - 150
      expect(manualPaidCount, equals(1));
      expect(manualUnpaidCount, equals(1));
    });
  });

  group('Payment Transaction Creation Tests', () {
    test('should create payment record when bill is marked as paid', () {
      // Create a bill marked as paid
      final bill = Bill.create(
        customerId: 1,
        startTime: DateTime.now(),
        endTime: DateTime.now().add(const Duration(hours: 2)),
        hourlyRate: 100.0,
      );
      bill.isPaid = true;

      // Verify bill properties
      expect(bill.isPaid, isTrue);
      expect(bill.isPartiallyPaid, isFalse);
      expect(bill.amount, equals(200.0));
      expect(bill.outstandingAmount, equals(0.0));
    });

    test('should create payment record when bill is marked as partially paid', () {
      // Create a bill marked as partially paid
      final bill = Bill.create(
        customerId: 1,
        startTime: DateTime.now(),
        endTime: DateTime.now().add(const Duration(hours: 2)),
        hourlyRate: 100.0,
      );
      bill.isPartiallyPaid = true;
      bill.partialAmount = 150.0;

      // Verify bill properties
      expect(bill.isPaid, isFalse);
      expect(bill.isPartiallyPaid, isTrue);
      expect(bill.partialAmount, equals(150.0));
      expect(bill.amount, equals(200.0));
      expect(bill.outstandingAmount, equals(50.0));
    });

    test('should determine correct payment amount for fully paid bill', () {
      // Test the logic from transaction_form_screen.dart
      const paymentStatus = 'paid';
      const billAmount = 200.0;
      const partialAmount = 0.0;

      // Logic from the transaction form
      final paymentAmount = paymentStatus == 'paid'
          ? billAmount
          : (paymentStatus == 'partial' ? partialAmount : 0.0);

      expect(paymentAmount, equals(200.0));
    });

    test('should determine correct payment amount for partially paid bill', () {
      // Test the logic from transaction_form_screen.dart
      const paymentStatus = 'partial';
      const billAmount = 200.0;
      const partialAmount = 150.0;

      // Logic from the transaction form
      final paymentAmount = paymentStatus == 'paid'
          ? billAmount
          : (paymentStatus == 'partial' ? partialAmount : 0.0);

      expect(paymentAmount, equals(150.0));
    });

    test('should determine correct payment amount for unpaid bill', () {
      // Test the logic from transaction_form_screen.dart
      const paymentStatus = 'unpaid';
      const billAmount = 200.0;
      const partialAmount = 0.0;

      // Logic from the transaction form
      final paymentAmount = paymentStatus == 'paid'
          ? billAmount
          : (paymentStatus == 'partial' ? partialAmount : 0.0);

      expect(paymentAmount, equals(0.0));
    });
  });

  group('Payment Model Tests', () {
    test('should create payment with correct properties', () {
      final payment = Payment.create(
        customerId: 1,
        billId: 123,
        paymentDate: DateTime.now(),
        amount: 200.0,
        paymentMethod: 'Cash',
        remarks: 'Payment for Bill #123 (Created as Paid)',
      );

      expect(payment.customerId, equals(1));
      expect(payment.billId, equals(123));
      expect(payment.amount, equals(200.0));
      expect(payment.paymentMethod, equals('Cash'));
      expect(payment.remarks, equals('Payment for Bill #123 (Created as Paid)'));
      expect(payment.hasBill, isTrue);
      expect(payment.isCreditPayment, isFalse);
    });
  });
}
